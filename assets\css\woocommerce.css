/*
WooCommerce Styles for DmrThema
========
*/

/* Basic WooCommerce Layout */
/* Genel WooCommerce layout - daha spesifik kurallar style.css'de tanimli */
.woocommerce .content-area {
    width: 100%;
}

.woocommerce-breadcrumb {
    padding: 1rem 0;
    margin-bottom: 1rem;
    font-size: 14px;
}

.woocommerce-breadcrumb a {
    color: #333;
    text-decoration: none;
}

.woocommerce-breadcrumb a:hover {
    text-decoration: underline;
}

/* Product Grid */
ul.products {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -15px;
    list-style: none;
    padding: 0;
}

ul.products li.product {
    padding: 0 15px 30px;
    margin-bottom: 0;
    list-style: none;
    position: relative;
}

@media (min-width: 768px) {
    .columns-2 ul.products li.product {
        width: 50%;
    }
    
    .columns-3 ul.products li.product {
        width: 33.333%;
    }
    
    .columns-4 ul.products li.product {
        width: 25%;
    }
}

@media (max-width: 767px) {
    ul.products li.product {
        width: 50%;
    }
}

/* Product Card */
ul.products li.product img {
    width: 100%;
    height: auto;
    transition: transform 0.3s ease;
}

ul.products li.product:hover img {
    transform: scale(1.05);
}

.woocommerce-loop-product__title {
    font-size: 16px;
    margin: 10px 0 5px;
    line-height: 1.4;
}

.woocommerce-loop-product__title a {
    color: #333;
    text-decoration: none;
}

.woocommerce-loop-product__title a:hover {
    color: #ff6000;
}

.price {
    font-size: 16px;
    font-weight: bold;
    color: #ff6000;
    margin-bottom: 10px;
}

.price del {
    color: #999;
    font-weight: normal;
}

/* Add to Cart Button */
.button, .add_to_cart_button {
    background: #ff6000;
    color: white;
    padding: 10px 20px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    text-decoration: none;
    display: inline-block;
    transition: all 0.3s ease;
}

.button:hover, .add_to_cart_button:hover {
    background: #e55500;
    color: white;
}

/* Urun kartlarindaki sepete ekle butonlari - hover efekti */
ul.products li.product .add_to_cart_button,
ul.products li.product .button {
    opacity: 0;
    visibility: hidden;
    transform: translateY(10px);
    transition: all 0.3s ease;
}

ul.products li.product:hover .add_to_cart_button,
ul.products li.product:hover .button {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

/* Sale Badge */
.onsale {
    display: none !important;
    position: absolute;
    top: 10px;
    left: 10px;
    background: #ff6000;
    color: white;
    padding: 5px 10px;
    border-radius: 3px;
    font-size: 12px;
    font-weight: bold;
    z-index: 1;
}

/* Shop Banner */
.shop-banner {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 60px 0;
    margin-bottom: 40px;
    position: relative;
    overflow: hidden;
}

.shop-banner::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.1);
    z-index: 1;
}

.shop-banner-content {
    position: relative;
    z-index: 2;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 40px;
}

.shop-banner-text {
    flex: 1;
    max-width: 600px;
}

.shop-banner-title {
    font-size: 3rem;
    font-weight: 700;
    margin: 0 0 20px 0;
    line-height: 1.2;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.shop-banner-description {
    font-size: 1.2rem;
    line-height: 1.6;
    margin: 0;
    opacity: 0.9;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.shop-banner-image {
    flex-shrink: 0;
    opacity: 0.8;
}

.shop-banner-image svg {
    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.2));
}

/* Sorting and Results */
.dmrthema-sorting {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    flex-wrap: wrap;
    gap: 15px;
    min-height: 40px;
}

/* WooCommerce orijinal margin kodunu kapat */
.woocommerce .woocommerce-result-count {
    /* margin: 0 0 1em; */
    /* margin: 0 !important; */
}

.woocommerce-result-count {
    color: #666;
    font-size: 14px;
    order: 1;
    flex: 1;
    display: flex;
    align-items: center;
    margin-left: -10px;
}

/* Sıralama ve Filtreler Sağda */
.sorting-filters-wrapper {
    display: flex;
    gap: 15px;
    order: 2;
    flex-shrink: 0;
}

.woocommerce-ordering {
    margin-bottom: 0;
    display: flex;
    align-items: center;
    gap: 10px;
}

.woocommerce-ordering select {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    background: white;
    height: 40px;
    box-sizing: border-box;
    min-width: 180px;
}

/* Shop Filters Button */
.shop-filters-toggle {
    background: #007cba;
    color: white;
    border: none;
    padding: 0 16px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    height: 40px;
    box-sizing: border-box;
    min-width: 120px;
    white-space: nowrap;
}

.shop-filters-toggle:hover {
    background: #005a87;
    transform: translateY(-1px);
}

.shop-filters-toggle i {
    font-size: 12px;
}

/* Shop Filters Toggle States */
.shop-filters-toggle.has-filters {
    background: #28a745 !important;
}

.shop-filters-toggle.has-filters:hover {
    background: #218838 !important;
}

/* Shop Filters Modal */
.shop-filters-modal {
    display: none;
    position: fixed;
    z-index: 9999;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(2px);
}

/* Modal acikken body scroll'unu engelle - daha iyi yontem */
body.modal-open {
    overflow: hidden !important;
    /* position: fixed kaldiriliyor - header/footer bozulmasini onlemek icin */
}

/* HTML elementine de overflow hidden ekle - tam scroll engelleme icin */
html.modal-open {
    overflow: hidden !important;
}

.shop-filters-modal-content {
    background-color: #fff;
    margin: 5% auto;
    padding: 0;
    border-radius: 8px;
    width: 90%;
    max-width: 600px;
    max-height: 80vh;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.shop-filters-modal-header {
    background: #f8f9fa;
    padding: 20px;
    border-bottom: 1px solid #dee2e6;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.shop-filters-modal-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #333;
}

.shop-filters-close {
    background: none;
    border: none;
    font-size: 20px;
    cursor: pointer;
    color: #666;
    padding: 5px;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.shop-filters-close:hover {
    background: #e9ecef;
    color: #333;
}

.shop-filters-modal-body {
    padding: 20px;
    max-height: 60vh;
    overflow-y: auto;
}

.filter-group {
    margin-bottom: 25px;
    padding-bottom: 20px;
    border-bottom: 1px solid #eee;
}

.filter-group:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.filter-group h4 {
    margin: 0 0 15px 0;
    font-size: 16px;
    font-weight: 600;
    color: #333;
}

.price-filter {
    display: flex;
    align-items: center;
    gap: 10px;
}

.price-filter input {
    flex: 1;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.price-filter span {
    color: #666;
    font-weight: 500;
}

.filter-checkbox {
    display: block;
    margin-bottom: 10px;
    cursor: pointer;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 5px 0;
    transition: color 0.2s ease;
}

.filter-checkbox:hover {
    color: #007cba;
}

.filter-checkbox input[type="checkbox"] {
    margin: 0;
    width: 16px;
    height: 16px;
    accent-color: #007cba;
}

.filter-checkbox span {
    flex: 1;
}

.shop-filters-modal-footer {
    background: #f8f9fa;
    padding: 20px;
    border-top: 1px solid #dee2e6;
    display: flex;
    justify-content: space-between;
    gap: 15px;
}

.btn-clear-filters,
.btn-apply-filters {
    padding: 10px 20px;
    border: none;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    flex: 1;
}

.btn-clear-filters {
    background: #6c757d;
    color: white;
}

.btn-clear-filters:hover {
    background: #5a6268;
}

.btn-apply-filters {
    background: #007cba;
    color: white;
}

.btn-apply-filters:hover {
    background: #005a87;
}

/* Responsive Design for Filters */
@media (max-width: 768px) {
    .shop-filters-modal-content {
        width: 95%;
        margin: 2% auto;
        max-height: 90vh;
    }

    .dmrthema-sorting {
        flex-direction: column;
        align-items: stretch;
        gap: 15px;
    }

    .woocommerce-result-count {
        text-align: center;
        order: 1;
    }

    .sorting-filters-wrapper {
        order: 2;
        justify-content: center;
        gap: 10px;
        flex-wrap: wrap;
    }

    .shop-filters-toggle {
        padding: 0 12px;
        font-size: 13px;
        height: 36px;
        min-width: 100px;
    }

    .woocommerce-ordering select {
        height: 36px;
        min-width: 150px;
    }

    .price-filter {
        flex-direction: column;
        gap: 8px;
    }

    .price-filter input {
        width: 100%;
    }

    .shop-filters-modal-footer {
        flex-direction: column;
    }
}

/* Pagination */
.woocommerce-pagination {
    text-align: center;
    margin-top: 30px;
}

.woocommerce-pagination .page-numbers {
    display: inline-block;
    padding: 8px 12px;
    margin: 0 2px;
    color: #333;
    text-decoration: none;
    border: 1px solid #ddd;
    border-radius: 3px;
}

.woocommerce-pagination .page-numbers:hover,
.woocommerce-pagination .page-numbers.current {
    background: #ff6000;
    color: white;
    border-color: #ff6000;
}

/* Messages */
.woocommerce-message,
.woocommerce-error,
.woocommerce-info {
    padding: 15px;
    margin-bottom: 20px;
    border-radius: 4px;
}

.woocommerce-message {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.woocommerce-error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.woocommerce-info {
    background: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
}

/* Cart */
.cart-contents {
    color: #333;
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 5px;
}

.cart-contents:hover {
    color: #ff6000;
}

.cart-contents .count {
    background: #ff6000;
    color: white;
    border-radius: 50%;
    padding: 2px 6px;
    font-size: 12px;
    min-width: 18px;
    text-align: center;
}

/* Product Categories */
ul.products li.product.product-category h2 {
    text-align: center;
    margin-top: 15px;
}

ul.products li.product.product-category a {
    display: block;
    text-decoration: none;
    color: #333;
}

ul.products li.product.product-category a:hover {
    color: #ff6000;
}

/* Star Rating */
.star-rating {
    font-size: 14px;
    color: #ff6000;
    margin-bottom: 5px;
}

/* Out of Stock */
.product-out-of-stock {
    position: absolute;
    top: 10px;
    right: 10px;
    background: rgba(0,0,0,0.7);
    color: white;
    padding: 5px 10px;
    border-radius: 3px;
    font-size: 12px;
}

/* Responsive */
@media (max-width: 768px) {
    .shop-banner {
        padding: 40px 0;
        margin-bottom: 30px;
    }

    .shop-banner-content {
        flex-direction: column;
        text-align: center;
        gap: 30px;
    }

    .shop-banner-title {
        font-size: 2.5rem;
    }

    .shop-banner-description {
        font-size: 1.1rem;
    }

    .shop-banner-image svg {
        width: 100px;
        height: 100px;
    }
}

@media (max-width: 480px) {
    ul.products li.product {
        width: 100%;
    }

    .dmrthema-sorting {
        flex-direction: column;
        align-items: stretch;
        gap: 15px;
    }

    .woocommerce-result-count {
        text-align: center;
        order: 1;
    }

    .sorting-filters-wrapper {
        order: 2;
        justify-content: center;
        flex-direction: column;
        gap: 10px;
    }

    .woocommerce-ordering {
        justify-content: center;
        width: 100%;
    }

    .woocommerce-ordering select {
        width: 100%;
        min-width: auto;
    }

    .shop-filters-toggle {
        width: 100%;
        justify-content: center;
        min-width: auto;
    }

    .shop-banner {
        padding: 30px 0;
        margin-bottom: 20px;
    }

    .shop-banner-title {
        font-size: 2rem;
    }

    .shop-banner-description {
        font-size: 1rem;
    }

    .shop-banner-image svg {
        width: 80px;
        height: 80px;
    }
}

/* SIDEBAR MINI CART - TUTARLI GORUNUM */
/* En yuksek oncelikli kurallar - tum CSS cakismalarini onler */
html body .cart-sidebar .widget_shopping_cart_content .woocommerce-mini-cart-item {
    padding: 12px 0 !important;
    margin: 0 !important;
    border-bottom: 1px solid #f0f0f0 !important;
    display: flex !important;
    align-items: flex-start !important;
}

html body .cart-sidebar .widget_shopping_cart_content .woocommerce-mini-cart-item .product-details {
    flex: 1 !important;
    padding-right: 25px !important;
}

html body .cart-sidebar .widget_shopping_cart_content .woocommerce-mini-cart-item .product-name {
    display: block !important;
    text-decoration: none !important;
    color: #333 !important;
    font-weight: 500 !important;
    margin-bottom: 5px !important;
    line-height: 1.3 !important;
}

html body .cart-sidebar .widget_shopping_cart_content .woocommerce-mini-cart-item .product-name:hover {
    color: #ff6000 !important;
}

html body .cart-sidebar .widget_shopping_cart_content .woocommerce-mini-cart-item .product-info {
    margin-top: 5px !important;
}

html body .cart-sidebar .widget_shopping_cart_content .woocommerce-mini-cart-item .quantity {
    color: #666 !important;
    font-size: 12px !important;
    display: block !important;
}

/* Tum sayfa tipleri icin ayni kurallar */
html body.home .cart-sidebar .widget_shopping_cart_content .woocommerce-mini-cart-item,
html body.shop .cart-sidebar .widget_shopping_cart_content .woocommerce-mini-cart-item,
html body.single-product .cart-sidebar .widget_shopping_cart_content .woocommerce-mini-cart-item,
html body.archive .cart-sidebar .widget_shopping_cart_content .woocommerce-mini-cart-item,
html body.woocommerce .cart-sidebar .widget_shopping_cart_content .woocommerce-mini-cart-item,
html body.woocommerce-shop .cart-sidebar .widget_shopping_cart_content .woocommerce-mini-cart-item,
html body.woocommerce-page .cart-sidebar .widget_shopping_cart_content .woocommerce-mini-cart-item {
    padding: 12px 0 !important;
    border-bottom: 1px solid #f0f0f0 !important;
}

/*
WooCommerce My Account Styles
=============================
*/
@media (min-width: 993px) {
    .woocommerce-account .content-area {
        width: 100%;
    }
    .woocommerce-MyAccount-navigation {
        float: left;
        width: 20% !important;
        margin-right: 5.**********%;
    }
    .woocommerce-MyAccount-content {
        float: right;
        width: 70% !important;
        padding-top: 0 !important;
    }
    .col2-set#customer_login,
    .col2-set.addresses {
        float: left;
        width: 100%;
        margin-right: 0;
        margin-left: 0
    }
    .col2-set#customer_login .col-1,
    .col2-set.addresses .col-1 {
        float: left;
        width: 41.**********%;
        margin-right: 5.**********%
    }
    .col2-set#customer_login .col-2,
    .col2-set.addresses .col-2 {
        float: right;
        width: 52.**********%;
        margin-right: 0
    }
    .woocommerce-MyAccount-content .form-row-first {
        float: left;
        width: 42.**********%;
        margin-right: 3.**********%;
    }
    .woocommerce-MyAccount-content .form-row-last {
        float: right;
        width: 53.**********%;
        margin-right: 0;
    }
}

.woocommerce-MyAccount-content {
    font-size: clamp(0.875rem, 0.8115rem + 0.2033vw, 0.9375rem); /* 14-15 */
}

/* -- Login/Register -- */
#customer_login p {
    font-size: 14px;
}
.woocommerce-ResetPassword p {
    font-size: 15px;
}
#customer_login p a {
    font-weight: bold;
    text-decoration: underline;
    text-underline-offset: 0.12em;
    text-decoration-thickness: 0.75px;
}
.form-row input.woocommerce-form__input-checkbox {
    float: left;
    margin: 5px 2px 0 0;
}
.woocommerce-account .woocommerce-form-login__rememberme span {
    margin-left: 0.5rem;
}
.woocommerce-account .col2-set .button {
    margin-top: 1rem;
}
.woocommerce-privacy-policy-text p {
    margin: 0;
}

/* -- Navigation -- */
.woocommerce-account .woocommerce-MyAccount-navigation {
    padding-left: 20px;
    padding-right: 20px;
    background-color: #F8F8F8;
    border-radius: 15px;
}

.woocommerce-MyAccount-navigation ul {
    margin-left: 0;
}
.woocommerce-MyAccount-navigation ul li {
    position: relative;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    list-style: none;
    font-size: clamp(0.875rem, 0.8115rem + 0.2033vw, 0.9375rem); /* 14-15 */
}
.woocommerce-MyAccount-navigation ul li:last-child {
    border: none;
}
.woocommerce-MyAccount-navigation ul li a {
    color: #555;
    display: block;
    padding: 0.675em 0 0.775em 0;
    text-decoration: none;
}
.woocommerce-MyAccount-navigation li a:hover,
.woocommerce-MyAccount-navigation li.is-active a {
    color: #222;
}
.woocommerce-MyAccount-navigation li.is-active a {
    font-weight: 600;
}
.woocommerce-MyAccount-navigation ul li a:before {
    background: #111;
    position: relative;
    top: 0 !important;
    float: right;
    content: "";
    width: 20px;
    height: 20px;
    opacity: 0.35;
    -webkit-mask-position: center;
            mask-position: center;
    -webkit-mask-repeat: no-repeat;
            mask-repeat: no-repeat;
    -webkit-mask-size: contain;
            mask-size: contain;
    transition: 0.2s all;
}
.woocommerce-MyAccount-navigation ul li a:hover:before,
.woocommerce-MyAccount-navigation ul li.is-active a:before {
    opacity: 1;
}

/* -- Icons -- */
.woocommerce-MyAccount-navigation ul li.woocommerce-MyAccount-navigation-link--dashboard a:before {
    -webkit-mask-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" version="1.1" viewBox="0 0 24 24"><g fill="none" fill-rule="evenodd" stroke="none" stroke-width="1"><path d="M0 0h24v24H0z"/><rect width="16" height="16" x="4" y="4" stroke="%********" stroke-linecap="round" stroke-width="1.5" rx="2"/><path d="M4 9h16M9 10v10" stroke="%********" stroke-linecap="round" stroke-width="1.5"/></g></svg>');
            mask-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" version="1.1" viewBox="0 0 24 24"><g fill="none" fill-rule="evenodd" stroke="none" stroke-width="1"><path d="M0 0h24v24H0z"/><rect width="16" height="16" x="4" y="4" stroke="%********" stroke-linecap="round" stroke-width="1.5" rx="2"/><path d="M4 9h16M9 10v10" stroke="%********" stroke-linecap="round" stroke-width="1.5"/></g></svg>');
}
.woocommerce-MyAccount-navigation ul li.woocommerce-MyAccount-navigation-link--orders a:before {
    -webkit-mask-image: url("data:image/svg+xml;charset=utf8,%3Csvg width='24' height='24' viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M9 5H7C5.89543 5 5 5.89543 5 7V19C5 20.1046 5.89543 21 7 21H17C18.1046 21 19 20.1046 19 19V7C19 5.89543 18.1046 5 17 5H15M9 5C9 6.10457 9.89543 7 11 7H13C14.1046 7 15 6.10457 15 5M9 5C9 3.89543 9.89543 3 11 3H13C14.1046 3 15 3.89543 15 5M12 12H15M12 16H15M9 12H9.01M9 16H9.01' stroke='%234A5568' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
            mask-image: url("data:image/svg+xml;charset=utf8,%3Csvg width='24' height='24' viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M9 5H7C5.89543 5 5 5.89543 5 7V19C5 20.1046 5.89543 21 7 21H17C18.1046 21 19 20.1046 19 19V7C19 5.89543 18.1046 5 17 5H15M9 5C9 6.10457 9.89543 7 11 7H13C14.1046 7 15 6.10457 15 5M9 5C9 3.89543 9.89543 3 11 3H13C14.1046 3 15 3.89543 15 5M12 12H15M12 16H15M9 12H9.01M9 16H9.01' stroke='%234A5568' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
}
.woocommerce-MyAccount-navigation ul li.woocommerce-MyAccount-navigation-link--downloads a:before {
    -webkit-mask-image: url("data:image/svg+xml;charset=utf8,%3Csvg width='24' height='24' viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M4 16L4 17C4 18.6569 5.34315 20 7 20L17 20C18.6569 20 20 18.6569 20 17L20 16M16 12L12 16M12 16L8 12M12 16L12 4' stroke='%234A5568' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
            mask-image: url("data:image/svg+xml;charset=utf8,%3Csvg width='24' height='24' viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M4 16L4 17C4 18.6569 5.34315 20 7 20L17 20C18.6569 20 20 18.6569 20 17L20 16M16 12L12 16M12 16L8 12M12 16L12 4' stroke='%234A5568' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
}
.woocommerce-MyAccount-navigation ul li.woocommerce-MyAccount-navigation-link--edit-address a:before {
    -webkit-mask-image: url("data:image/svg+xml;charset=utf8,%3Csvg width='24' height='24' viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M3 12L5 10M5 10L12 3L19 10M5 10V20C5 20.5523 5.44772 21 6 21H9M19 10L21 12M19 10V20C19 20.5523 18.5523 21 18 21H15M9 21C9.55228 21 10 20.5523 10 20V16C10 15.4477 10.4477 15 11 15H13C13.5523 15 14 15.4477 14 16V20C14 20.5523 14.4477 21 15 21M9 21H15' stroke='%234A5568' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
            mask-image: url("data:image/svg+xml;charset=utf8,%3Csvg width='24' height='24' viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M3 12L5 10M5 10L12 3L19 10M5 10V20C5 20.5523 5.44772 21 6 21H9M19 10L21 12M19 10V20C19 20.5523 18.5523 21 18 21H15M9 21C9.55228 21 10 20.5523 10 20V16C10 15.4477 10.4477 15 11 15H13C13.5523 15 14 15.4477 14 16V20C14 20.5523 14.4477 21 15 21M9 21H15' stroke='%234A5568' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
}
.woocommerce-MyAccount-navigation ul li.woocommerce-MyAccount-navigation-link--payment-methods a:before {
    -webkit-mask-image: url("data:image/svg+xml;charset=utf8,%3Csvg width='24' height='24' viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M3 10H21M7 15H8M12 15H13M6 19H18C19.6569 19 21 17.6569 21 16V8C21 6.34315 19.6569 5 18 5H6C4.34315 5 3 6.34315 3 8V16C3 17.6569 4.34315 19 6 19Z' stroke='%234A5568' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
            mask-image: url("data:image/svg+xml;charset=utf8,%3Csvg width='24' height='24' viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M3 10H21M7 15H8M12 15H13M6 19H18C19.6569 19 21 17.6569 21 16V8C21 6.34315 19.6569 5 18 5H6C4.34315 5 3 6.34315 3 8V16C3 17.6569 4.34315 19 6 19Z' stroke='%234A5568' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
}
.woocommerce-MyAccount-navigation ul li.woocommerce-MyAccount-navigation-link--edit-account a:before {
    -webkit-mask-image: url("data:image/svg+xml;charset=utf8,%3Csvg width='24' height='24' viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M5.12104 17.8037C7.15267 16.6554 9.4998 16 12 16C14.5002 16 16.8473 16.6554 18.879 17.8037M15 10C15 11.6569 13.6569 13 12 13C10.3431 13 9 11.6569 9 10C9 8.34315 10.3431 7 12 7C13.6569 7 15 8.34315 15 10ZM21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z' stroke='%234A5568' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
            mask-image: url("data:image/svg+xml;charset=utf8,%3Csvg width='24' height='24' viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M5.12104 17.8037C7.15267 16.6554 9.4998 16 12 16C14.5002 16 16.8473 16.6554 18.879 17.8037M15 10C15 11.6569 13.6569 13 12 13C10.3431 13 9 11.6569 9 10C9 8.34315 10.3431 7 12 7C13.6569 7 15 8.34315 15 10ZM21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z' stroke='%234A5568' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
}
.woocommerce-MyAccount-navigation ul li.woocommerce-MyAccount-navigation-link--customer-logout a:before {
    -webkit-mask-image: url("data:image/svg+xml;charset=utf8,%3Csvg width='24' height='24' viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M17 16L21 12M21 12L17 8M21 12L7 12M13 16V17C13 18.6569 11.6569 20 10 20H6C4.34315 20 3 18.6569 3 17V7C3 5.34315 4.34315 4 6 4H10C11.6569 4 13 5.34315 13 7V8' stroke='%********' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
            mask-image: url("data:image/svg+xml;charset=utf8,%3Csvg width='24' height='24' viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M17 16L21 12M21 12L17 8M21 12L7 12M13 16V17C13 18.6569 11.6569 20 10 20H6C4.34315 20 3 18.6569 3 17V7C3 5.34315 4.34315 4 6 4H10C11.6569 4 13 5.34315 13 7V8' stroke='%********' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
}
.woocommerce-MyAccount-navigation ul li.woocommerce-MyAccount-navigation-link--wishlist a:before,
.woocommerce-MyAccount-navigation ul li.woocommerce-MyAccount-navigation-link--cgkit-wishlist a:before {
    -webkit-mask-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" xml:space="preserve" fill="%23000" version="1.1" viewBox="0 0 471.701 471.701"><path d="M433.601 67.001c-24.7-24.7-57.4-38.2-92.3-38.2s-67.7 13.6-92.4 38.3l-12.9 12.9-13.1-13.1c-24.7-24.7-57.6-38.4-92.5-38.4-34.8 0-67.6 13.6-92.2 38.2-24.7 24.7-38.3 57.5-38.2 92.4 0 34.9 13.7 67.6 38.4 92.3l187.8 187.8c2.6 2.6 6.1 4 9.5 4 3.4 0 6.9-1.3 9.5-3.9l188.2-187.5c24.7-24.7 38.3-57.5 38.3-92.4.1-34.9-13.4-67.7-38.1-92.4zm-19.2 165.7-178.7 178-178.3-178.3c-19.6-19.6-30.4-45.6-30.4-73.3s10.7-53.7 30.3-73.2c19.5-19.5 45.5-30.3 73.1-30.3 27.7 0 53.8 10.8 73.4 30.4l22.6 22.6c5.3 5.3 13.8 5.3 19.1 0l22.4-22.4c19.6-19.6 45.7-30.4 73.3-30.4 27.6 0 53.6 10.8 73.2 30.3 19.6 19.6 30.3 45.6 30.3 73.3.1 27.7-10.7 53.7-30.3 73.3z"/></svg>');
            mask-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" xml:space="preserve" fill="%23000" version="1.1" viewBox="0 0 471.701 471.701"><path d="M433.601 67.001c-24.7-24.7-57.4-38.2-92.3-38.2s-67.7 13.6-92.4 38.3l-12.9 12.9-13.1-13.1c-24.7-24.7-57.6-38.4-92.5-38.4-34.8 0-67.6 13.6-92.2 38.2-24.7 24.7-38.3 57.5-38.2 92.4 0 34.9 13.7 67.6 38.4 92.3l187.8 187.8c2.6 2.6 6.1 4 9.5 4 3.4 0 6.9-1.3 9.5-3.9l188.2-187.5c24.7-24.7 38.3-57.5 38.3-92.4.1-34.9-13.4-67.7-38.1-92.4zm-19.2 165.7-178.7 178-178.3-178.3c-19.6-19.6-30.4-45.6-30.4-73.3s10.7-53.7 30.3-73.2c19.5-19.5 45.5-30.3 73.1-30.3 27.7 0 53.8 10.8 73.4 30.4l22.6 22.6c5.3 5.3 13.8 5.3 19.1 0l22.4-22.4c19.6-19.6 45.7-30.4 73.3-30.4 27.6 0 53.6 10.8 73.2 30.3 19.6 19.6 30.3 45.6 30.3 73.3.1 27.7-10.7 53.7-30.3 73.3z"/></svg>');
}
.woocommerce-MyAccount-navigation ul li.woocommerce-MyAccount-navigation-link--subscriptions a:before,
.woocommerce-MyAccount-navigation ul li.woocommerce-MyAccount-navigation-link--wc-smart-coupons a:before {
    -webkit-mask-image: url("data:image/svg+xml;charset=utf8,%3Csvg width='24' height='24' viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M12 8C10.3431 8 9 8.89543 9 10C9 11.1046 10.3431 12 12 12C13.6569 12 15 12.8954 15 14C15 15.1046 13.6569 16 12 16M12 8C13.1104 8 14.0799 8.4022 14.5987 9M12 8V7M12 8L12 16M12 16L12 17M12 16C10.8896 16 9.92008 15.5978 9.40137 15M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z' stroke='%234A5568' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
            mask-image: url("data:image/svg+xml;charset=utf8,%3Csvg width='24' height='24' viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M12 8C10.3431 8 9 8.89543 9 10C9 11.1046 10.3431 12 12 12C13.6569 12 15 12.8954 15 14C15 15.1046 13.6569 16 12 16M12 8C13.1104 8 14.0799 8.4022 14.5987 9M12 8V7M12 8L12 16M12 16L12 17M12 16C10.8896 16 9.92008 15.5978 9.40137 15M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z' stroke='%234A5568' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
}

/* -- Links -- */
.woocommerce-account .woocommerce-MyAccount-content p a,
.addresses header.title a {
    text-decoration: underline;
    text-decoration-thickness: 0.75px;
    text-underline-offset: 0.12em;
}

/* -- Headings -- */
.woocommerce-account h2 {
    font-size: clamp(1.375rem, 1.1209rem + 0.813vw, 1.625rem); /* 22-26 */
}

/* -- Order Details Table List -- */
.woocommerce-account .woocommerce-orders-table th {
    padding: 10px 15px 10px 0;
    border-bottom: 1px solid #eee;
    color: #222;
    font-size: 14px;
}
.woocommerce-account table td {
    font-size: 14px;
}
.woocommerce-account tr.woocommerce-orders-table__row td {
    padding: 10px 15px 10px 0;
    border-bottom: 1px solid #f2f2f2;
}
.woocommerce-account tr.woocommerce-orders-table__row td.woocommerce-orders-table__cell-order-actions {
    text-align: right;
    padding-right: 0;
}
.woocommerce-account .woocommerce-orders-table tr th:first-child,
.woocommerce-account .woocommerce-orders-table tr td:first-child {
    padding-left: 0;
}
.woocommerce-account .woocommerce-orders-table tr th:last-child {
    text-align: right;
    padding-right: 0;
}
td.woocommerce-orders-table__cell-order-number a {
    font-weight: bold;
    text-decoration: underline;
}
tr.woocommerce-orders-table__row--status-completed td.woocommerce-orders-table__cell-order-status {
    color: #25841e;
    font-weight: 600;
}
.my_account_orders .button,
.woocommerce-MyAccount-downloads .button {
    margin-right: 0.236em;
    padding: 0.6180469716em 0.875em;
    font-size: 12px;
}

/* -- Downloads -- */
.woocommerce-account .woocommerce-order-downloads table thead th {
    padding: 0 20px 10px 0;
    border-bottom: 1px solid #eee;
}
.woocommerce-account .woocommerce-order-downloads td {
    padding: 10px 20px 10px 0;
    border-bottom: 1px solid #eee;
}
.woocommerce-account .woocommerce-order-downloads a.button.alt {
    font-size: 13px;
    margin: 0;
}

/* -- Pagination -- */
.woocommerce-MyAccount-content .woocommerce-Pagination {
    float: none;
    text-align: center;
    display: flex;
    justify-content: space-between;
}
.woocommerce-MyAccount-content .woocommerce-Pagination a {
    padding: 0.7em 1.5em;
    background-color: #fff;
    color: #111;
    border: 1px solid #e2e2e2;
    border-radius: 4px;
    font-size: 13px;
    font-weight: 600;
}
.woocommerce-MyAccount-content .woocommerce-Pagination a:only-child {
    margin-left: auto;
}
.woocommerce-MyAccount-content .woocommerce-Pagination a:hover {
    border-color: #ccc;
}
.woocommerce-MyAccount-content .woocommerce-Pagination a.woocommerce-button--previous:before {
    margin-right: 5px;
    content: "\2190";
}
.woocommerce-MyAccount-content .woocommerce-Pagination a.woocommerce-button--next:after {
    margin-left: 5px;
    content: "\2192";
}

/* -- Single Order Details -- */
.woocommerce-order-details {
    margin-bottom: 2rem;
    padding: 2rem;
    border: 1px solid #e2e2e2;
}
.woocommerce-order-details table {
    margin-bottom: 0;
    font-size: 14px;
}
.woocommerce-order-details table th,
.woocommerce-order-details table td {
    padding: 0.5rem 0;
}
.woocommerce-order-details table a {
    font-weight: 600;
}
.woocommerce-order-details table strong {
    font-weight: normal;
}
.woocommerce-order-details table tfoot th,
.woocommerce-order-details table tfoot td {
    border-bottom: none;
    padding-bottom: 0;
}
.woocommerce-order-details th,
.woocommerce-order-details td {
    padding-top: 10px;
    border-bottom: 1px solid #eee;
}
.woocommerce-order-details th:last-child,
.woocommerce-order-details td:last-child {
    text-align: right;
}
.woocommerce-order-details tfoot tr:last-child th,
.woocommerce-order-details tfoot tr:last-child td {
    border: none;
    font-size: 18px;
    font-weight: bold;
}
.woocommerce-account .entry-content:not(.wc-tab) .woocommerce-order-details p {
    font-size: 13px;
}
.woocommerce-order-details .wc-item-meta {
    margin-top: 5px;
    margin-bottom: 0px;
}
.woocommerce-order-details .wc-item-meta li,
.woocommerce-account .entry-content:not(.wc-tab) .woocommerce-order-details .wc-item-meta p {
    font-size: 12px;
}
.woocommerce-order-details .wc-item-meta li {
    margin-bottom: 0;
}
.woocommerce-MyAccount-content mark {
    font-weight: 600;
}
.wc-item-meta {
    margin-top: 10px;
    margin-left: 0;
    font-size: 0.875em;
    list-style: none;
}
.wc-item-meta li p, .wc-item-meta li strong {
    display: inline-block;
    margin: 0;
}
.woocommerce-account .woocommerce-MyAccount-content p.order-again {
    margin: 1.5rem 0 0 0;
}
.woocommerce-account .woocommerce-MyAccount-content p.order-again a {
    text-decoration: none;
    font-size: 14px;
}

/* -- Addresses -- */
.woocommerce-Address-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}
.woocommerce-Address-title h3 {
    margin-bottom: 0;
}
.addresses header.title a {
    font-size: 14px;
    margin-left: 0.5rem;
}
.woocommerce-account p#billing_address_1_field,
.woocommerce-account p#shipping_address_1_field {
    margin-bottom: 0.5rem;
}

/* -- Account Details -- */
.woocommerce-account fieldset {
    margin: 0;
    padding: 0;
}
.woocommerce-MyAccount-content p em {
    display: inline-block;
    margin-top: 0.5rem;
    font-size: 12px;
    font-style: normal;
}

/* -- Notices -- */
.woocommerce-account .woocommerce-info .button {
    font-size: 14px;
}

/* -- Password Strength -- */
.woocommerce-password-hint {
    display: block;
    font-size: 12px;
    padding-top: 0.5rem;
}

/* -- Hesabim Sayfasi Gizlenen Elementler -- */
#post-15 > div > div > div > p:nth-child(2),
#post-15 > div > div > div > p:nth-child(3) {
    display: none !important;
}
.woocommerce form .password-input {
    position: relative;
}
.woocommerce form .password-input input[type="password"] {
    padding-right: 2.5rem;
}
.woocommerce-page form .show-password-input {
    position: absolute;
    right: 0.7em;
    top: 0px;
    cursor: pointer;
}
.woocommerce-page form .show-password-input:after {
    content: '';
    display: block;
    -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke-width='1.5' stroke='currentColor' class='w-6 h-6'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' d='M2.036 12.322a1.012 1.012 0 010-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178z' /%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' d='M15 12a3 3 0 11-6 0 3 3 0 016 0z' /%3E%3C/svg%3E");
            mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke-width='1.5' stroke='currentColor' class='w-6 h-6'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' d='M2.036 12.322a1.012 1.012 0 010-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178z' /%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' d='M15 12a3 3 0 11-6 0 3 3 0 016 0z' /%3E%3C/svg%3E");
    -webkit-mask-position: 50%;
            mask-position: 50%;
    -webkit-mask-repeat: no-repeat;
            mask-repeat: no-repeat;
    -webkit-mask-size: 1em;
            mask-size: 1em;
    min-height: 1.2em;
    min-width: 1.2em;
    top: 0;
    z-index: 1;
    background: #999;
}
.woocommerce-password-strength {
    padding: 0.5407911001em 0 0 0;
    font-size: 0.875em;
}
.woocommerce-password-strength.strong {
    color: #0f834d;
}
.woocommerce-password-strength.bad,
.woocommerce-password-strength.short {
    color: #e2401c;
}
.woocommerce-password-strength.good {
    color: #3d9cd2;
}

/* -- My Account Responsive Design -- */
@media screen and (max-width: 992px) {
    .woocommerce-account .site-content {
        padding-bottom: 3rem;
    }
    .u-column2.col-2 {
        margin-top: 2rem;
    }
    .woocommerce-MyAccount-content table {
        margin: 0;
    }
    .woocommerce-MyAccount-content table thead {
        display: none;
    }
    .woocommerce-MyAccount-content table tr {
        display: block;
        margin-bottom: 0.625em;
    }
    .woocommerce-MyAccount-content table td {
        display: block;
    }
    .woocommerce-MyAccount-content table td:before {
        content: attr(data-label);
        float: left;
        font-weight: bold;
        text-transform: uppercase;
    }
    .woocommerce-MyAccount-content table td:last-child {
        border-bottom: 0;
    }
    .woocommerce-order-details table tfoot th, .woocommerce-order-details table tfoot td {
        padding-bottom: 0.5rem;
    }
    .woocommerce-order-details td.woocommerce-table__product-total,
    .woocommerce-order-details tfoot td:last-child {
        text-align: left;
    }
    .woocommerce-account .order_details tfoot tr td {
        border-top: 1px solid #eee;
    }
    .woocommerce-account .order_details tfoot tr:first-child th,
    .woocommerce-order-details th {
        padding-bottom: 0.5rem;
    }
    .woocommerce-account tr.woocommerce-orders-table__row td.woocommerce-orders-table__cell-order-actions {
        text-align: left;
    }
}

/* columns-3 icindeki div'lere dmrthema-sorting stilini uygula */
body > div.container > div.columns-3 > div {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    flex-wrap: wrap;
    gap: 15px;
    min-height: 40px;
}

/* columns-3 icindeki div'lerin responsive tasarimi */
@media (max-width: 768px) {
    body > div.container > div.columns-3 > div {
        flex-direction: column;
        align-items: stretch;
        gap: 15px;
    }

    body > div.container > div.columns-3 > div .woocommerce-result-count {
        text-align: center;
        order: 1;
    }

    body > div.container > div.columns-3 > div .sorting-filters-wrapper {
        order: 2;
        justify-content: center;
        gap: 10px;
        flex-wrap: wrap;
    }

    body > div.container > div.columns-3 > div .shop-filters-toggle {
        padding: 0 12px;
        font-size: 13px;
        height: 36px;
        min-width: 100px;
    }

    body > div.container > div.columns-3 > div .woocommerce-ordering select {
        height: 36px;
        min-width: 150px;
    }
}

@media (max-width: 480px) {
    body > div.container > div.columns-3 > div {
        flex-direction: column;
        align-items: stretch;
        gap: 15px;
    }

    body > div.container > div.columns-3 > div .woocommerce-result-count {
        text-align: center;
        order: 1;
    }

    body > div.container > div.columns-3 > div .sorting-filters-wrapper {
        order: 2;
        justify-content: center;
        flex-direction: column;
        gap: 10px;
    }

    body > div.container > div.columns-3 > div .woocommerce-ordering {
        justify-content: center;
        width: 100%;
    }

    body > div.container > div.columns-3 > div .woocommerce-ordering select {
        width: 100%;
        min-width: auto;
    }

    body > div.container > div.columns-3 > div .shop-filters-toggle {
        width: 100%;
        justify-content: center;
        min-width: auto;
    }
}

/* columns-3 icindeki div'lerin icerigini duzenle */
body > div.container > div.columns-3 > div .woocommerce-result-count {
    color: #666;
    font-size: 14px;
    order: 1;
    flex: 1;
    display: flex;
    align-items: center;
    margin-left: -10px;
}

body > div.container > div.columns-3 > div .sorting-filters-wrapper {
    display: flex;
    gap: 15px;
    order: 2;
    flex-shrink: 0;
}

body > div.container > div.columns-3 > div .woocommerce-ordering {
    margin-bottom: 0;
    display: flex;
    align-items: center;
    gap: 10px;
}

body > div.container > div.columns-3 > div .woocommerce-ordering select {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    background: white;
    height: 40px;
    box-sizing: border-box;
    min-width: 180px;
}
